agent:
  config:
    max_tokens: 1500
    temperature: 0.1
  model: gpt-4.1-mini
  type: langgraph
isactive: 1 # 0 active, 1 test, 2 inactive
database:
  sql:
    #connection_string: mssql+pyodbc://ai-read:CHXG7jS2y9X4@10.1.1.83:1433/VERSISDB?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes
    connection_string: "DRIVER={ODBC Driver 17 for SQL Server};SERVER=10.1.1.114\\ATLASMAINDB;DATABASE=VERSISDB;UID=netaxtech;PWD=Net@xAdmin22!;TrustServerCertificate=yes;"

    tables:
      - __EFMigrationsHistory
      - AcademicTitles
      - AggregatedCounter
      - Audits
      - birimlist
      - Counter
      - Datas
      - DataStatus
      - Departments
      - Employee
      - Faculties
      - Groups
      - Hash
      - Indicators
      - Job
      - JobParameter
      - JobQueue
      - LinkedPools
      - List
      - Menus
      - PeriodEntryTypes
      - Periods
      - Pools
      - RoleClaims
      - Roles
      - RootValues
      - Schema
      - Server
      - Set
      - State
      - SubTypes
      - Types
      - UserClaims
      - UserLogins
      - UserRoles
      - Users
      - UserTokens

description:
  A bot developed by Atlas University in Istanbul, Turkey, designed to
  assist administrators with institutional data and operations.
metadata:
  audience: administrators
  institution: Atlas University
  languages:
    - English
    - Turkish
  location: Istanbul, Turkey
  topics:
    - staff information
    - departmental data
    - budget analysis
    - facility management
    - institutional operations
name: AdminBot
prompts:
  query_prompt_path: admin_bot/query.txt
  system_prompt_path: admin_bot/system.txt
tools:
  - config:
      collection_name: admin_documents
      persist_directory: ./chroma_db/admin
      top_k: 5
    enabled: false
    type: DocumentSearchTool
  - config:
      allowed_tables:
        - __EFMigrationsHistory
        - AcademicTitles
        - AggregatedCounter
        - Audits
        - birimlist
        - Counter
        - Datas
        - DataStatus
        - Departments
        - Employee
        - Faculties
        - Groups
        - Hash
        - Indicators
        - Job
        - JobParameter
        - JobQueue
        - LinkedPools
        - List
        - Menus
        - PeriodEntryTypes
        - Periods
        - Pools
        - RoleClaims
        - Roles
        - RootValues
        - Schema
        - Server
        - Set
        - State
        - SubTypes
        - Types
        - UserClaims
        - UserLogins
        - UserRoles
        - Users
        - UserTokens

      # Remote SQL Server database (VERSISDB)
      # connection_string: mssql+pyodbc://ai-read:CHXG7jS2y9X4@10.1.1.83:1433/VERSISDB?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes
      connection_string: "DRIVER={ODBC Driver 17 for SQL Server};SERVER=10.1.1.114\\ATLASMAINDB;DATABASE=VERSISDB;UID=netaxtech;PWD=Net@xAdmin22!;TrustServerCertificate=yes;"
      max_results: 50
    enabled: true
    type: SQLQueryTool
  - config:
      max_results: 3
      search_depth: basic
    enabled: true
    type: WebSearchTool
